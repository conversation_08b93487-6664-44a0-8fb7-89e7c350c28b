import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/providers/travel/document/document_providers.dart';
import 'package:culture_connect/screens/travel/document/document_screens.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';

class MockTravelDocumentProvider extends Mock implements TravelDocumentProvider {}
class MockDocumentReminderProvider extends Mock implements DocumentReminderProvider {}
class MockVisaRequirementProvider extends Mock implements VisaRequirementProvider {}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Travel Documents Flow Integration Tests', () {
    late MockTravelDocumentProvider mockDocumentProvider;
    late MockDocumentReminderProvider mockReminderProvider;
    late MockVisaRequirementProvider mockVisaRequirementProvider;

    setUp(() {
      mockDocumentProvider = MockTravelDocumentProvider();
      mockReminderProvider = MockDocumentReminderProvider();
      mockVisaRequirementProvider = MockVisaRequirementProvider();
    });

    testWidgets('Complete travel documents flow', (WidgetTester tester) async {
      // Setup mock data
      final testPassport = Passport(
        id: 'passport1',
        userId: 'user1',
        name: 'US Passport',
        documentNumber: 'AB123456',
        issuedBy: 'Department of State',
        issuedDate: DateTime(2020, 1, 1),
        expiryDate: DateTime(2030, 1, 1),
        status: TravelDocumentStatus.valid,
        documentImageUrls: ['https://example.com/passport.jpg'],
        createdAt: DateTime(2020, 1, 1),
        updatedAt: DateTime(2020, 1, 1),
        nationality: 'United States',
        countryCode: 'US',
        placeOfBirth: 'New York',
        dateOfBirth: DateTime(1990, 1, 1),
        gender: 'M',
      );

      final testVisa = Visa(
        id: 'visa1',
        userId: 'user1',
        name: 'Japan Tourist Visa',
        documentNumber: 'V123456',
        issuedBy: 'Embassy of Japan',
        issuedDate: DateTime(2023, 1, 1),
        expiryDate: DateTime(2023, 7, 1),
        status: TravelDocumentStatus.valid,
        documentImageUrls: ['https://example.com/visa.jpg'],
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
        visaType: VisaType.tourist,
        entryType: VisaEntryType.single,
        countryOfIssue: 'United States',
        countryValidFor: 'Japan',
        maxStayDuration: 90,
      );

      // Setup mock provider responses
      when(mockDocumentProvider.isLoading).thenReturn(false);
      when(mockDocumentProvider.error).thenReturn(null);
      when(mockDocumentProvider.documents).thenReturn([testPassport, testVisa]);
      when(mockDocumentProvider.passports).thenReturn([testPassport]);
      when(mockDocumentProvider.visas).thenReturn([testVisa]);
      when(mockDocumentProvider.initialize()).thenAnswer((_) async {});
      when(mockDocumentProvider.loadDocuments()).thenAnswer((_) async {});
      when(mockDocumentProvider.getDocument('passport1')).thenAnswer((_) async => testPassport);
      when(mockDocumentProvider.getDocument('visa1')).thenAnswer((_) async => testVisa);
      when(mockDocumentProvider.deleteDocument(any)).thenAnswer((_) async => true);

      when(mockReminderProvider.isLoading).thenReturn(false);
      when(mockReminderProvider.error).thenReturn(null);
      when(mockReminderProvider.reminders).thenReturn([]);
      when(mockReminderProvider.initialize()).thenAnswer((_) async {});
      when(mockReminderProvider.loadReminders()).thenAnswer((_) async {});

      when(mockVisaRequirementProvider.isLoading).thenReturn(false);
      when(mockVisaRequirementProvider.error).thenReturn(null);
      when(mockVisaRequirementProvider.initialize()).thenAnswer((_) async {});

      // Build our app and trigger a frame
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<TravelDocumentProvider>.value(value: mockDocumentProvider),
            ChangeNotifierProvider<DocumentReminderProvider>.value(value: mockReminderProvider),
            ChangeNotifierProvider<VisaRequirementProvider>.value(value: mockVisaRequirementProvider),
          ],
          child: MaterialApp(
            home: const TravelDocumentsScreen(),
            routes: {
              '/document_details': (context) => const DocumentDetailsScreen(documentId: 'passport1'),
              '/document_upload': (context) => const DocumentUploadScreen(documentType: TravelDocumentType.passport),
              '/document_reminders': (context) => const DocumentRemindersScreen(),
              '/visa_requirements': (context) => const VisaRequirementsScreen(),
            },
          ),
        ),
      );

      // Wait for the screen to load
      await tester.pumpAndSettle();

      // Verify the initial screen shows the documents
      expect(find.text('Travel Documents'), findsOneWidget);
      expect(find.text('Passports'), findsOneWidget);
      expect(find.text('Visas'), findsOneWidget);
      expect(find.text('US Passport'), findsOneWidget);

      // Tap on the passport to view details
      await tester.tap(find.text('US Passport'));
      await tester.pumpAndSettle();

      // Verify the document details screen
      expect(find.text('US Passport'), findsOneWidget);
      expect(find.text('Document Information'), findsOneWidget);
      expect(find.text('Passport Information'), findsOneWidget);
      expect(find.text('AB123456'), findsOneWidget);
      expect(find.text('Department of State'), findsOneWidget);
      expect(find.text('United States'), findsOneWidget);

      // Go back to the documents screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Switch to the Visas tab
      await tester.tap(find.text('Visas'));
      await tester.pumpAndSettle();

      // Verify the visas tab shows the visa
      expect(find.text('Japan Tourist Visa'), findsOneWidget);

      // Tap on the visa to view details
      await tester.tap(find.text('Japan Tourist Visa'));
      await tester.pumpAndSettle();

      // Verify the visa details screen
      expect(find.text('Japan Tourist Visa'), findsOneWidget);
      expect(find.text('Document Information'), findsOneWidget);
      expect(find.text('Visa Information'), findsOneWidget);
      expect(find.text('V123456'), findsOneWidget);
      expect(find.text('Embassy of Japan'), findsOneWidget);
      expect(find.text('Tourist'), findsOneWidget);
      expect(find.text('Single'), findsOneWidget);
      expect(find.text('Japan'), findsOneWidget);

      // Go back to the documents screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Tap the FAB to add a new document
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Verify the add document dialog
      expect(find.text('Add Travel Document'), findsOneWidget);
      expect(find.text('Passport'), findsOneWidget);
      expect(find.text('Visa'), findsOneWidget);
      expect(find.text('ID Card'), findsOneWidget);

      // Tap on Passport to add a new passport
      await tester.tap(find.text('Passport'));
      await tester.pumpAndSettle();

      // Verify the document upload screen
      expect(find.text('Add Passport'), findsOneWidget);
      expect(find.text('Document Information'), findsOneWidget);
      expect(find.text('Passport Information'), findsOneWidget);
      expect(find.text('Document Name'), findsOneWidget);
      expect(find.text('Document Number'), findsOneWidget);
      expect(find.text('Issued By'), findsOneWidget);
      expect(find.text('Issue Date'), findsOneWidget);
      expect(find.text('Expiry Date'), findsOneWidget);
      expect(find.text('Nationality'), findsOneWidget);
      expect(find.text('Country Code'), findsOneWidget);
      expect(find.text('Place of Birth'), findsOneWidget);
      expect(find.text('Date of Birth'), findsOneWidget);
      expect(find.text('Gender'), findsOneWidget);
      expect(find.text('Document Images'), findsOneWidget);
      expect(find.text('Add Document'), findsOneWidget);

      // Go back to the documents screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Tap on the reminders icon
      await tester.tap(find.byIcon(Icons.notifications));
      await tester.pumpAndSettle();

      // Verify the reminders screen
      expect(find.text('Document Reminders'), findsOneWidget);
      expect(find.text('Due'), findsOneWidget);
      expect(find.text('Upcoming'), findsOneWidget);

      // Go back to the documents screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Tap on the visa requirements icon
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();

      // Verify the visa requirements screen
      expect(find.text('Visa Requirements'), findsOneWidget);
      expect(find.text('Check Visa Requirements'), findsOneWidget);
      expect(find.text('From (Nationality)'), findsOneWidget);
      expect(find.text('To (Destination)'), findsOneWidget);
      expect(find.text('Check Requirements'), findsOneWidget);
    });
  });
}
